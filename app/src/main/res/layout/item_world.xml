<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/bg_rounded_card"
    android:orientation="vertical"
    android:padding="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/world_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/misans"
            android:text="World Name"
            android:textColor="@color/on_surface"
            android:textSize="16sp"
            android:textStyle="bold" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/world_rename_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_settings"
                android:contentDescription="@string/rename_world"
                app:tint="@color/on_surface" />

            <ImageButton
                android:id="@+id/world_export_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_down"
                android:contentDescription="@string/export_world"
                app:tint="@color/on_surface" />

            <ImageButton
                android:id="@+id/world_backup_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_arrow_down"
                android:rotation="180"
                android:contentDescription="@string/backup_world"
                app:tint="@color/on_surface" />

            <ImageButton
                android:id="@+id/world_delete_button"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                android:contentDescription="@string/delete_world"
                app:tint="@color/error" />

        </LinearLayout>

    </LinearLayout>

    <TextView
        android:id="@+id/world_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/misans"
        android:text="Game Mode: Survival"
        android:textColor="@color/on_surface"
        android:textSize="12sp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/world_size"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/misans"
            android:text="Size: 15.2 MB"
            android:textColor="@color/on_surface"
            android:textSize="11sp" />

        <TextView
            android:id="@+id/world_last_played"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/misans"
            android:text="Last played: Dec 25, 2024 14:30"
            android:textColor="@color/on_surface"
            android:textSize="11sp" />

    </LinearLayout>

</LinearLayout>
