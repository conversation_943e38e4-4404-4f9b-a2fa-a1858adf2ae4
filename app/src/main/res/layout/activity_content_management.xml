<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/card_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_arrow_down"
            android:rotation="90"
            app:tint="@color/on_surface" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/misans"
                android:text="@string/content_management"
                android:textColor="@color/on_surface"
                android:textSize="20sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/version_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/misans"
                android:text="@string/not_found_version"
                android:textColor="@color/on_surface"
                android:textSize="14sp" />

        </LinearLayout>

        <Button
            android:id="@+id/import_world_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:backgroundTint="@color/primary"
            android:text="@string/import_world"
            android:textColor="@color/on_primary"
            android:textSize="12sp"
            android:visibility="visible" />

        <Button
            android:id="@+id/import_pack_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:backgroundTint="@color/primary"
            android:text="@string/import_resource_pack"
            android:textColor="@color/on_primary"
            android:textSize="12sp"
            android:visibility="gone" />

    </LinearLayout>

    <!-- Tab Layout -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:background="@drawable/card_background"
        app:tabGravity="fill"
        app:tabMode="fixed"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="@color/on_surface" />

    <!-- Content Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="12dp">

        <!-- Worlds RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/worlds_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="4dp"
            android:visibility="visible" />

        <!-- Resource Packs RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/resource_packs_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="4dp"
            android:visibility="gone" />

        <!-- Behavior Packs RecyclerView -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/behavior_packs_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:padding="4dp"
            android:visibility="gone" />

    </FrameLayout>

    <!-- Status Bar -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@drawable/card_background"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="12dp">

        <TextView
            android:id="@+id/status_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:fontFamily="@font/misans"
            android:text="Ready"
            android:textColor="@color/on_surface"
            android:textSize="12sp" />

    </LinearLayout>

</LinearLayout>
