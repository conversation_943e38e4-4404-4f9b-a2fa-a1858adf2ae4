<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/bg_rounded_card"
    android:orientation="vertical"
    android:padding="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/pack_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/misans"
                android:text="Resource Pack Name"
                android:textColor="@color/on_surface"
                android:textSize="16sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/pack_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:background="@drawable/bg_abi_default"
                    android:fontFamily="@font/misans"
                    android:gravity="center"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp"
                    android:text="Resource Pack"
                    android:textColor="@android:color/white"
                    android:textSize="10sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/pack_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/misans"
                    android:text="2.5 MB"
                    android:textColor="@color/on_surface"
                    android:textSize="11sp" />

            </LinearLayout>

        </LinearLayout>

        <Switch
            android:id="@+id/pack_enable_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp" />

        <ImageButton
            android:id="@+id/pack_delete_button"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_delete"
            android:contentDescription="@string/delete_resource_pack"
            app:tint="@color/error" />

    </LinearLayout>

    <TextView
        android:id="@+id/pack_description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:fontFamily="@font/misans"
        android:text="Pack description goes here"
        android:textColor="@color/on_surface"
        android:textSize="12sp" />

</LinearLayout>
