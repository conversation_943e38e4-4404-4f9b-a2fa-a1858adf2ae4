{
    find_programver = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = "14.0.7"
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolld"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    find_program = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_utils.binary.deplibs"] = {
        ["llvm-objdump"] = false,
        objdump = [[C:\msys64\usr\bin\objdump.exe]]
    },
    ["detect.sdks.find_android_sdk"] = {
        sdk = { }
    },
    ["core.tools.gcc.has_cflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["-fprofile-sample-accurate"] = true,
            ["-ffixed-a6"] = true,
            ["-D"] = true,
            ["-mfix-cmse-cve-2021-35465"] = true,
            ["-ffixed-x10"] = true,
            ["-fborland-extensions"] = true,
            ["-fdiagnostics-show-hotness"] = true,
            ["-objcmt-ns-nonatomic-iosonly"] = true,
            ["-MF"] = true,
            ["-gline-directives-only"] = true,
            ["-fsanitize-address-outline-instrumentation"] = true,
            ["-fgpu-defer-diag"] = true,
            ["-mignore-xcoff-visibility"] = true,
            ["-fmodules-validate-input-files-content"] = true,
            ["-ffixed-d2"] = true,
            ["-MMD"] = true,
            ["-mfentry"] = true,
            ["-ffixed-x7"] = true,
            ["-fno-show-source-location"] = true,
            ["-E"] = true,
            ["-mno-lvi-hardening"] = true,
            ["-mpackets"] = true,
            ["-finstrument-function-entry-bare"] = true,
            ["-fpascal-strings"] = true,
            ["-ffixed-x24"] = true,
            ["-ivfsoverlay"] = true,
            ["-ffixed-x25"] = true,
            ["-fsjlj-exceptions"] = true,
            ["-mmark-bti-property"] = true,
            ["-fdelete-null-pointer-checks"] = true,
            ["-Xlinker"] = true,
            ["-fno-profile-generate"] = true,
            ["-fvisibility-from-dllstorageclass"] = true,
            ["-fprotect-parens"] = true,
            ["-Qn"] = true,
            ["-g"] = true,
            ["-print-search-dirs"] = true,
            ["-fdiagnostics-show-template-tree"] = true,
            ["-F"] = true,
            ["-working-directory"] = true,
            ["-cl-fp32-correctly-rounded-divide-sqrt"] = true,
            ["--no-gpu-bundle-output"] = true,
            ["-fglobal-isel"] = true,
            ["-objcmt-migrate-property-dot-syntax"] = true,
            ["--cuda-host-only"] = true,
            ["-Xpreprocessor"] = true,
            ["-objcmt-migrate-all"] = true,
            ["-b"] = true,
            ["-fembed-bitcode"] = true,
            ["-cl-strict-aliasing"] = true,
            ["-fenable-matrix"] = true,
            ["-nobuiltininc"] = true,
            ["-mllvm"] = true,
            ["-mtgsplit"] = true,
            ["-isystem"] = true,
            ["-fprofile-instr-generate"] = true,
            ["-fdelayed-template-parsing"] = true,
            ["-fstandalone-debug"] = true,
            ["-fno-autolink"] = true,
            ["-mrecord-mcount"] = true,
            ["-fpch-codegen"] = true,
            ["-mno-movt"] = true,
            ["-fmodules-search-all"] = true,
            ["-fstrict-enums"] = true,
            ["-mextern-sdata"] = true,
            ["-Xcuda-fatbinary"] = true,
            ["-fno-color-diagnostics"] = true,
            ["-faddrsig"] = true,
            ["-cl-denorms-are-zero"] = true,
            ["-fobjc-disable-direct-methods-for-testing"] = true,
            ["-ffixed-x20"] = true,
            ["-fvalidate-ast-input-files-content"] = true,
            ["-fno-rtti-data"] = true,
            ["-mnocrc"] = true,
            ["-mno-packets"] = true,
            ["-trigraphs"] = true,
            ["-objcmt-migrate-property"] = true,
            ["-mcmse"] = true,
            ["-verify-pch"] = true,
            ["--cuda-compile-host-device"] = true,
            ["-fopenmp-target-debug"] = true,
            ["-miamcu"] = true,
            ["-arch"] = true,
            ["-fmodules-validate-system-headers"] = true,
            ["-ftrapv"] = true,
            ["-gdwarf-2"] = true,
            ["-fno-legacy-pass-manager"] = true,
            ["-mno-nvj"] = true,
            ["-fno-keep-static-consts"] = true,
            ["-fstrict-float-cast-overflow"] = true,
            ["-malign-double"] = true,
            ["-fdebug-ranges-base-address"] = true,
            ["-fsanitize-memory-track-origins"] = true,
            ["-fno-direct-access-external-data"] = true,
            ["-fno-coverage-mapping"] = true,
            ["-fdebug-macro"] = true,
            ["-fno-profile-instr-generate"] = true,
            ["-print-ivar-layout"] = true,
            ["-fapple-link-rtlib"] = true,
            ["-fcall-saved-x15"] = true,
            ["-fgnu-runtime"] = true,
            ["-ffixed-x21"] = true,
            ["-c"] = true,
            ["-fno-temp-file"] = true,
            ["-fno-access-control"] = true,
            ["-fsanitize-memory-param-retval"] = true,
            ["-MD"] = true,
            ["-objcmt-migrate-annotation"] = true,
            ["-fexperimental-strict-floating-point"] = true,
            ["-mno-msa"] = true,
            ["-mno-hvx"] = true,
            ["-fcxx-exceptions"] = true,
            ["-fminimize-whitespace"] = true,
            ["-B"] = true,
            ["-mskip-rax-setup"] = true,
            ["-fwritable-strings"] = true,
            ["-fno-cuda-approx-transcendentals"] = true,
            ["-fmodules-user-build-path"] = true,
            ["-fjump-tables"] = true,
            ["-fno-exceptions"] = true,
            ["-freciprocal-math"] = true,
            ["-mno-code-object-v3"] = true,
            ["-module-dependency-dir"] = true,
            ["-objcmt-migrate-designated-init"] = true,
            ["-objcmt-migrate-instancetype"] = true,
            ["-fvisibility-global-new-delete-hidden"] = true,
            ["-extract-api"] = true,
            ["-ffixed-r19"] = true,
            ["-fsanitize-address-poison-custom-array-cookie"] = true,
            ["-C"] = true,
            ["-fmodules-validate-once-per-build-session"] = true,
            ["-frwpi"] = true,
            ["--gpu-bundle-output"] = true,
            ["-fno-pch-codegen"] = true,
            ["-fdiagnostics-show-option"] = true,
            ["-fverbose-asm"] = true,
            ["-fshort-enums"] = true,
            ["-fno-sanitize-hwaddress-experimental-aliasing"] = true,
            ["-fobjc-arc"] = true,
            ["-mno-abicalls"] = true,
            ["-save-temps"] = true,
            ["-MV"] = true,
            ["--emit-static-lib"] = true,
            ["-mfp32"] = true,
            ["-fno-signed-char"] = true,
            ["-gdwarf-5"] = true,
            ["-fxray-always-emit-typedevents"] = true,
            ["-fdigraphs"] = true,
            ["-fsanitize-stats"] = true,
            ["-fvisibility-ms-compat"] = true,
            ["-dI"] = true,
            ["-fwasm-exceptions"] = true,
            ["-fallow-editor-placeholders"] = true,
            ["-objcmt-migrate-subscripting"] = true,
            ["-mstack-arg-probe"] = true,
            ["-fno-finite-loops"] = true,
            ["-nohipwrapperinc"] = true,
            ["-mbranches-within-32B-boundaries"] = true,
            ["-funique-internal-linkage-names"] = true,
            ["-fcall-saved-x8"] = true,
            ["-fno-assume-sane-operator-new"] = true,
            ["-rewrite-objc"] = true,
            ["-Xassembler"] = true,
            ["-menable-experimental-extensions"] = true,
            ["-Qunused-arguments"] = true,
            ["-isystem-after"] = true,
            ["-fdirect-access-external-data"] = true,
            ["-membedded-data"] = true,
            ["--precompile"] = true,
            ["-freg-struct-return"] = true,
            ["-gcodeview"] = true,
            ["-fno-elide-constructors"] = true,
            ["-mpacked-stack"] = true,
            ["-z"] = true,
            ["-fno-declspec"] = true,
            ["-mnop-mcount"] = true,
            ["-fsanitize-cfi-icall-generalize-pointers"] = true,
            ["-ffixed-x23"] = true,
            ["-msoft-float"] = true,
            ["-fregister-global-dtors-with-atexit"] = true,
            ["-gno-embed-source"] = true,
            ["-fvisibility-inlines-hidden-static-local-var"] = true,
            ["-mlong-calls"] = true,
            ["-fcs-profile-generate"] = true,
            ["-flto"] = true,
            ["-meabi"] = true,
            ["-rpath"] = true,
            ["-mfix-cortex-a53-835769"] = true,
            ["-fno-strict-return"] = true,
            ["--cuda-path-ignore-env"] = true,
            ["-fno-sanitize-address-poison-custom-array-cookie"] = true,
            ["-fno-offload-lto"] = true,
            ["-mno-long-calls"] = true,
            ["-finline-functions"] = true,
            ["-mnvj"] = true,
            ["-fsanitize-trap"] = true,
            ["--help-hidden"] = true,
            ["-cl-no-signed-zeros"] = true,
            ["-fno-new-infallible"] = true,
            ["-mrestrict-it"] = true,
            ["-fsycl"] = true,
            ["-fno-sanitize-memory-track-origins"] = true,
            ["-Xcuda-ptxas"] = true,
            ["-cl-mad-enable"] = true,
            ["-fms-extensions"] = true,
            ["-isysroot"] = true,
            ["-fcall-saved-x9"] = true,
            ["-mno-lvi-cfi"] = true,
            ["-ffixed-x3"] = true,
            ["-frelaxed-template-template-args"] = true,
            ["-fno-jump-tables"] = true,
            ["-mno-hvx-qfloat"] = true,
            ["-include-pch"] = true,
            ["-fapplication-extension"] = true,
            ["-fhip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-fsystem-module"] = true,
            ["-fno-strict-float-cast-overflow"] = true,
            ["-mnvs"] = true,
            ["-faapcs-bitfield-load"] = true,
            ["-I-"] = true,
            ["-mno-hvx-ieee-fp"] = true,
            ["-cl-fast-relaxed-math"] = true,
            ["-mno-embedded-data"] = true,
            ["-fapple-kext"] = true,
            ["-fno-common"] = true,
            ["-mmadd4"] = true,
            ["-ffixed-point"] = true,
            ["-ffunction-sections"] = true,
            ["-fno-short-wchar"] = true,
            ["--analyzer-output"] = true,
            ["-arcmt-migrate-emit-errors"] = true,
            ["-mhvx-ieee-fp"] = true,
            ["-fmodules-strict-decluse"] = true,
            ["-mrtd"] = true,
            ["-mabicalls"] = true,
            ["-fsanitize-thread-func-entry-exit"] = true,
            ["-fno-split-machine-functions"] = true,
            ["-fsanitize-thread-atomics"] = true,
            ["--end-no-unused-arguments"] = true,
            ["-fno-aapcs-bitfield-width"] = true,
            ["-mno-relax"] = true,
            ["-munsafe-fp-atomics"] = true,
            ["-cl-no-stdinc"] = true,
            ["-ffixed-a0"] = true,
            ["-mno-outline-atomics"] = true,
            ["-faapcs-bitfield-width"] = true,
            ["-mcrc"] = true,
            ["-fmath-errno"] = true,
            ["-fsigned-char"] = true,
            ["-fstack-protector-strong"] = true,
            ["-fxray-link-deps"] = true,
            ["-mno-gpopt"] = true,
            ["-fstack-size-section"] = true,
            ["-fdata-sections"] = true,
            ["-ffixed-d0"] = true,
            ["-fno-sanitize-address-outline-instrumentation"] = true,
            ["-fno-discard-value-names"] = true,
            ["-fno-sanitize-thread-atomics"] = true,
            ["-fno-delayed-template-parsing"] = true,
            ["-arcmt-migrate-report-output"] = true,
            ["-fwhole-program-vtables"] = true,
            ["-fno-sanitize-memory-param-retval"] = true,
            ["-fno-operator-names"] = true,
            ["--start-no-unused-arguments"] = true,
            ["-fno-sanitize-trap"] = true,
            ["-static-openmp"] = true,
            ["-iwithprefix"] = true,
            ["-ffixed-x30"] = true,
            ["-pedantic"] = true,
            ["-nogpulib"] = true,
            ["-Tbss"] = true,
            ["-ffine-grained-bitfield-accesses"] = true,
            ["-fno-threadsafe-statics"] = true,
            ["-ffixed-a5"] = true,
            ["-pg"] = true,
            ["-fxray-always-emit-customevents"] = true,
            ["-mwavefrontsize64"] = true,
            ["-fno-preserve-as-comments"] = true,
            ["-funique-basic-block-section-names"] = true,
            ["-mno-fix-cortex-a53-835769"] = true,
            ["-v"] = true,
            ["-index-header-map"] = true,
            ["-finline-hint-functions"] = true,
            ["-fpseudo-probe-for-profiling"] = true,
            ["-Tdata"] = true,
            ["-mno-outline"] = true,
            ["-U"] = true,
            ["-fconvergent-functions"] = true,
            ["-fmodules-decluse"] = true,
            ["-traditional-cpp"] = true,
            ["-fbuiltin-module-map"] = true,
            ["-feliminate-unused-debug-types"] = true,
            ["-fno-digraphs"] = true,
            ["-mno-stack-arg-probe"] = true,
            ["-pthread"] = true,
            ["-include"] = true,
            ["-fdiagnostics-print-source-range-info"] = true,
            ["-fno-double-square-bracket-attributes"] = true,
            ["-ffixed-d3"] = true,
            ["-ftrigraphs"] = true,
            ["-fdiagnostics-absolute-paths"] = true,
            ["-ffixed-x28"] = true,
            ["-print-effective-triple"] = true,
            ["-fgnu89-inline"] = true,
            ["-fno-split-stack"] = true,
            ["-mno-nvs"] = true,
            ["-fsanitize-address-use-odr-indicator"] = true,
            ["-objcmt-migrate-readonly-property"] = true,
            ["-fcommon"] = true,
            ["-shared-libsan"] = true,
            ["-fintegrated-cc1"] = true,
            ["-MG"] = true,
            ["-fno-use-cxa-atexit"] = true,
            ["-mcode-object-v3"] = true,
            ["-MJ"] = true,
            ["-fno-sanitize-ignorelist"] = true,
            ["-P"] = true,
            ["-fno-sanitize-cfi-cross-dso"] = true,
            ["-mno-tls-direct-seg-refs"] = true,
            ["-nostdinc"] = true,
            ["-fpch-validate-input-files-content"] = true,
            ["-ffinite-loops"] = true,
            ["-nogpuinc"] = true,
            ["-ffixed-x14"] = true,
            ["-ffixed-x17"] = true,
            ["-fexperimental-new-constant-interpreter"] = true,
            ["-fobjc-weak"] = true,
            ["-enable-trivial-auto-var-init-zero-knowing-it-will-be-removed-from-clang"] = true,
            ["-msvr4-struct-return"] = true,
            ["-fansi-escape-codes"] = true,
            ["-fsized-deallocation"] = true,
            ["-ffixed-a4"] = true,
            ["-mibt-seal"] = true,
            ["-mrelax"] = true,
            ["-fdollars-in-identifiers"] = true,
            ["-fsanitize-cfi-cross-dso"] = true,
            ["-imacros"] = true,
            ["-fno-spell-checking"] = true,
            ["-fno-stack-protector"] = true,
            ["-fapprox-func"] = true,
            ["-fshort-wchar"] = true,
            ["-mno-save-restore"] = true,
            ["-fgpu-flush-denormals-to-zero"] = true,
            ["-iwithsysroot"] = true,
            ["-fstack-protector"] = true,
            ["-mno-mt"] = true,
            ["-MP"] = true,
            ["-fno-delete-null-pointer-checks"] = true,
            ["-menable-unsafe-fp-math"] = true,
            ["-fno-sycl"] = true,
            ["-femit-all-decls"] = true,
            ["-fexceptions"] = true,
            ["-ffixed-x2"] = true,
            ["-fprofile-generate"] = true,
            ["-fno-hip-new-launch-api"] = true,
            ["--config"] = true,
            ["-mno-restrict-it"] = true,
            ["-fno-fine-grained-bitfield-accesses"] = true,
            ["-fno-diagnostics-fixit-info"] = true,
            ["-maix-struct-return"] = true,
            ["-fobjc-exceptions"] = true,
            ["-fsplit-lto-unit"] = true,
            ["-mhvx-qfloat"] = true,
            ["-fno-hip-fp32-correctly-rounded-divide-sqrt"] = true,
            ["-mno-wavefrontsize64"] = true,
            ["-moutline"] = true,
            ["-fno-sanitize-stats"] = true,
            ["-CC"] = true,
            ["-mamdgpu-ieee"] = true,
            ["-mbackchain"] = true,
            ["-mexecute-only"] = true,
            ["-fno-objc-infer-related-result-type"] = true,
            ["-cl-finite-math-only"] = true,
            ["-fno-dollars-in-identifiers"] = true,
            ["-fno-force-enable-int128"] = true,
            ["-ffixed-x18"] = true,
            ["--version"] = true,
            ["-fno-sanitize-cfi-canonical-jump-tables"] = true,
            ["-cl-unsafe-math-optimizations"] = true,
            ["-fignore-exceptions"] = true,
            ["-fno-memory-profile"] = true,
            ["-T"] = true,
            ["-fdebug-types-section"] = true,
            ["-faligned-allocation"] = true,
            ["-fsplit-stack"] = true,
            ["-ftime-trace"] = true,
            ["-fno-rtlib-add-rpath"] = true,
            ["-ffixed-a2"] = true,
            ["-fno-plt"] = true,
            ["-fno-integrated-cc1"] = true,
            ["-mmsa"] = true,
            ["-gcodeview-ghash"] = true,
            ["-mno-local-sdata"] = true,
            ["-ffast-math"] = true,
            ["-fno-use-init-array"] = true,
            ["-mno-implicit-float"] = true,
            ["-objcmt-migrate-literals"] = true,
            ["-fdwarf-exceptions"] = true,
            ["-cl-kernel-arg-info"] = true,
            ["-fdouble-square-bracket-attributes"] = true,
            ["-frtlib-add-rpath"] = true,
            ["-MQ"] = true,
            ["-ffixed-a3"] = true,
            ["-serialize-diagnostics"] = true,
            ["-fno-gpu-defer-diag"] = true,
            ["-fasync-exceptions"] = true,
            ["-mqdsp6-compat"] = true,
            ["-mno-memops"] = true,
            ["-fsanitize-address-use-after-scope"] = true,
            ["-help"] = true,
            ["-fropi"] = true,
            ["-fgpu-rdc"] = true,
            ["-mincremental-linker-compatible"] = true,
            ["-fsanitize-address-globals-dead-stripping"] = true,
            ["-fcolor-diagnostics"] = true,
            ["-fcall-saved-x12"] = true,
            ["-print-resource-dir"] = true,
            ["-fstack-clash-protection"] = true,
            ["-ffixed-r9"] = true,
            ["-fsanitize-hwaddress-experimental-aliasing"] = true,
            ["-ffixed-x26"] = true,
            ["-objcmt-migrate-protocol-conformance"] = true,
            ["-fno-eliminate-unused-debug-types"] = true,
            ["-fno-sanitize-address-use-odr-indicator"] = true,
            ["-ibuiltininc"] = true,
            ["-ffixed-x16"] = true,
            ["--migrate"] = true,
            ["-mfp64"] = true,
            ["--hip-link"] = true,
            ["-static-libsan"] = true,
            ["-iframeworkwithsysroot"] = true,
            ["-fopenmp"] = true,
            ["-msave-restore"] = true,
            ["-L"] = true,
            ["-mno-incremental-linker-compatible"] = true,
            ["-fseh-exceptions"] = true,
            ["-ffixed-x9"] = true,
            ["-fprebuilt-implicit-modules"] = true,
            ["-gdwarf-4"] = true,
            ["-mlong-double-80"] = true,
            ["-fpcc-struct-return"] = true,
            ["-mtls-direct-seg-refs"] = true,
            ["-fno-constant-cfstrings"] = true,
            ["-gdwarf"] = true,
            ["-o"] = true,
            ["-funroll-loops"] = true,
            ["-fno-complete-member-pointers"] = true,
            ["-Wdeprecated"] = true,
            ["-fgnu-keywords"] = true,
            ["-fms-hotpatch"] = true,
            ["-fxray-ignore-loops"] = true,
            ["-fno-signed-zeros"] = true,
            ["-fcxx-modules"] = true,
            ["-gdwarf-3"] = true,
            ["-finstrument-functions-after-inlining"] = true,
            ["-fcomplete-member-pointers"] = true,
            ["-fno-xray-function-index"] = true,
            ["-mlvi-hardening"] = true,
            ["-munaligned-access"] = true,
            ["-dD"] = true,
            ["-fno-pch-debuginfo"] = true,
            ["-fdebug-info-for-profiling"] = true,
            ["-gline-tables-only"] = true,
            ["-dsym-dir"] = true,
            ["-ffixed-d5"] = true,
            ["-fzvector"] = true,
            ["-M"] = true,
            ["-ffixed-x6"] = true,
            ["-undef"] = true,
            ["-Qy"] = true,
            ["-iprefix"] = true,
            ["-fsplit-machine-functions"] = true,
            ["-fno-trigraphs"] = true,
            ["-fno-builtin"] = true,
            ["-fvisibility-inlines-hidden"] = true,
            ["-fmodules-disable-diagnostic-validation"] = true,
            ["-fmodules"] = true,
            ["-cl-opt-disable"] = true,
            ["-mno-extern-sdata"] = true,
            ["-fmodules-ts"] = true,
            ["-ffixed-x15"] = true,
            ["-mno-ms-bitfields"] = true,
            ["-ffixed-x13"] = true,
            ["-fno-elide-type"] = true,
            ["-mno-global-merge"] = true,
            ["-fcoverage-mapping"] = true,
            ["-finstrument-functions"] = true,
            ["-fno-integrated-as"] = true,
            ["-objcmt-allowlist-dir-path"] = true,
            ["-fno-global-isel"] = true,
            ["-mno-unaligned-access"] = true,
            ["-fno-crash-diagnostics"] = true,
            ["-fno-register-global-dtors-with-atexit"] = true,
            ["-fno-stack-clash-protection"] = true,
            ["-fcall-saved-x11"] = true,
            ["-module-file-info"] = true,
            ["-ffixed-x8"] = true,
            ["-fno-gnu-inline-asm"] = true,
            ["-fsanitize-thread-memory-access"] = true,
            ["-fstack-usage"] = true,
            ["-mhvx"] = true,
            ["-cxx-isystem"] = true,
            ["-fno-pseudo-probe-for-profiling"] = true,
            ["-ffreestanding"] = true,
            ["-G"] = true,
            ["-fstack-protector-all"] = true,
            ["--no-cuda-version-check"] = true,
            ["-S"] = true,
            ["-objcmt-migrate-readwrite-property"] = true,
            ["-fobjc-arc-exceptions"] = true,
            ["-fno-visibility-inlines-hidden-static-local-var"] = true,
            ["-flegacy-pass-manager"] = true,
            ["-ffixed-d1"] = true,
            ["-fsplit-dwarf-inlining"] = true,
            ["-fdiscard-value-names"] = true,
            ["-pipe"] = true,
            ["-fno-gpu-allow-device-init"] = true,
            ["-mno-crc"] = true,
            ["-fvirtual-function-elimination"] = true,
            ["-freroll-loops"] = true,
            ["-ffixed-a1"] = true,
            ["-x"] = true,
            ["-fblocks"] = true,
            ["-mno-bti-at-return-twice"] = true,
            ["-mthread-model"] = true,
            ["-ffixed-x12"] = true,
            ["-gmodules"] = true,
            ["-fno-fixed-point"] = true,
            ["-fno-sanitize-address-use-after-scope"] = true,
            ["-ffixed-x31"] = true,
            ["-fembed-bitcode-marker"] = true,
            ["-fsanitize-cfi-canonical-jump-tables"] = true,
            ["-fdiagnostics-show-note-include-stack"] = true,
            ["-fmemory-profile"] = true,
            ["-fno-unique-section-names"] = true,
            ["-fno-zero-initialized-in-bss"] = true,
            ["-fcall-saved-x14"] = true,
            ["-dependency-dot"] = true,
            ["-print-targets"] = true,
            ["-fgpu-sanitize"] = true,
            ["-mno-madd4"] = true,
            ["-mgpopt"] = true,
            ["-H"] = true,
            ["-fno-openmp-extensions"] = true,
            ["-moutline-atomics"] = true,
            ["-fno-merge-all-constants"] = true,
            ["-mlong-double-128"] = true,
            ["-ffixed-x27"] = true,
            ["-print-libgcc-file-name"] = true,
            ["-momit-leaf-frame-pointer"] = true,
            ["-iquote"] = true,
            ["-fmerge-all-constants"] = true,
            ["-ffixed-x11"] = true,
            ["-fxray-instrument"] = true,
            ["-fcall-saved-x18"] = true,
            ["-fcall-saved-x13"] = true,
            ["-fcuda-short-ptr"] = true,
            ["-print-multiarch"] = true,
            ["-fno-show-column"] = true,
            ["-fno-sanitize-thread-func-entry-exit"] = true,
            ["-femulated-tls"] = true,
            ["--verify-debug-info"] = true,
            ["-ffixed-d6"] = true,
            ["-gembed-source"] = true,
            ["-fxl-pragma-pack"] = true,
            ["-fcf-protection"] = true,
            ["-rewrite-legacy-objc"] = true,
            ["-emit-module"] = true,
            ["-fcoroutines-ts"] = true,
            ["-fstrict-vtable-pointers"] = true,
            ["-fno-lto"] = true,
            ["-fopenmp-extensions"] = true,
            ["-mno-tgsplit"] = true,
            ["-I"] = true,
            ["-fopenmp-target-new-runtime"] = true,
            ["-fpch-debuginfo"] = true,
            ["-fuse-line-directives"] = true,
            ["-fsanitize-memory-use-after-dtor"] = true,
            ["-fshow-skipped-includes"] = true,
            ["-Xclang"] = true,
            ["-cl-uniform-work-group-size"] = true,
            ["-fno-standalone-debug"] = true,
            ["-time"] = true,
            ["-w"] = true,
            ["-gno-inline-line-tables"] = true,
            ["-fopenmp-simd"] = true,
            ["-fhip-new-launch-api"] = true,
            ["-mlvi-cfi"] = true,
            ["-mlocal-sdata"] = true,
            ["-mstackrealign"] = true,
            ["-fapple-pragma-pack"] = true,
            ["-mno-fix-cmse-cve-2021-35465"] = true,
            ["-fintegrated-as"] = true,
            ["-fpch-instantiate-templates"] = true,
            ["--cuda-device-only"] = true,
            ["-ffixed-d7"] = true,
            ["-fsave-optimization-record"] = true,
            ["-foffload-lto"] = true,
            ["-MT"] = true,
            ["-ffixed-x22"] = true,
            ["-fcall-saved-x10"] = true,
            ["-fdiagnostics-parseable-fixits"] = true,
            ["-fnew-infallible"] = true,
            ["-fforce-emit-vtables"] = true,
            ["-mgeneral-regs-only"] = true,
            ["-fslp-vectorize"] = true,
            ["-mms-bitfields"] = true,
            ["-cl-single-precision-constant"] = true,
            ["-emit-llvm"] = true,
            ["-mno-execute-only"] = true,
            ["-idirafter"] = true,
            ["-mno-neg-immediates"] = true,
            ["-Xanalyzer"] = true,
            ["-dM"] = true,
            ["-mno-cumode"] = true,
            ["-ffixed-x5"] = true,
            ["-save-stats"] = true,
            ["-MM"] = true,
            ["-dependency-file"] = true,
            ["-fno-cxx-modules"] = true,
            ["-fms-compatibility"] = true,
            ["-ffixed-x1"] = true,
            ["-ffixed-x4"] = true,
            ["-fno-sanitize-memory-use-after-dtor"] = true,
            ["-print-runtime-dir"] = true,
            ["-mmt"] = true,
            ["-fforce-dwarf-frame"] = true,
            ["-fvectorize"] = true,
            ["-fno-rtti"] = true,
            ["-fno-profile-instr-use"] = true,
            ["-print-supported-cpus"] = true,
            ["-fgpu-allow-device-init"] = true,
            ["-objcmt-atomic-property"] = true,
            ["-objcmt-returns-innerpointer-property"] = true,
            ["-fno-unroll-loops"] = true,
            ["-fobjc-encode-cxx-class-template-spec"] = true,
            ["-fcuda-approx-transcendentals"] = true,
            ["-mno-seses"] = true,
            ["-emit-ast"] = true,
            ["-print-rocm-search-dirs"] = true,
            ["-iwithprefixbefore"] = true,
            ["-mcumode"] = true,
            ["-ffixed-d4"] = true,
            ["-fno-addrsig"] = true,
            ["-emit-merged-ifs"] = true,
            ["-fimplicit-module-maps"] = true,
            ["-mrelax-all"] = true,
            ["-ffixed-x19"] = true,
            ["-gdwarf32"] = true,
            ["--analyze"] = true,
            ["-fforce-enable-int128"] = true,
            ["-forder-file-instrumentation"] = true,
            ["-print-target-triple"] = true,
            ["-mmemops"] = true,
            ["-Xopenmp-target"] = true,
            ["-objcmt-migrate-ns-macros"] = true,
            ["-Ttext"] = true,
            ["-fdeclspec"] = true,
            ["-relocatable-pch"] = true,
            ["-fno-sanitize-thread-memory-access"] = true,
            ["-mglobal-merge"] = true,
            ["-mlong-double-64"] = true,
            ["-mseses"] = true,
            ["-emit-interface-stubs"] = true,
            ["-fno-debug-macro"] = true,
            ["-ffixed-x29"] = true,
            ["-fkeep-static-consts"] = true,
            ["-gdwarf64"] = true,
            ["--cuda-noopt-device-debug"] = true
        }
    },
    ["lib.detect.has_flags"] = {
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-O3"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_sh__-shared -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb -llog --target=armv7-none-linux-androideabi21 -nostdlib++ -mthumb_-fPIC"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-DNDEBUG"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx__--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-Oz"] = true,
        ["android_armeabi-v7a_C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7_cxx_cxflags_--sysroot=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot -isystem C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\sysroot\\usr\\include\\arm-linux-androideabi -nostdinc++ -Qunused-arguments --target=armv7-none-linux-androideabi21 -mthumb_-std=c++20"] = true
    },
    ["detect.sdks.find_ndk"] = {
        ndk = {
            cross = "arm-linux-androideabi-",
            llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
            bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
            sdkver = "21",
            sdkdir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
            ndkver = 25,
            sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]]
        }
    },
    ["core.tools.gcc.has_ldflags"] = {
        ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\25.2.9519653\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++_14.0.7"] = {
            ["--no-allow-multiple-definition"] = true,
            ["--dynamicbase"] = true,
            ["--version"] = true,
            ["-static"] = true,
            ["--tsaware"] = true,
            ["--large-address-aware"] = true,
            ["--appcontainer"] = true,
            ["--strip-debug"] = true,
            ["-L"] = true,
            ["--insert-timestamp"] = true,
            ["--no-insert-timestamp"] = true,
            ["--Bdynamic"] = true,
            ["-s"] = true,
            ["-v"] = true,
            ["--nxcompat"] = true,
            ["--disable-high-entropy-va"] = true,
            ["--export-all-symbols"] = true,
            ["--no-whole-archive"] = true,
            ["--no-seh"] = true,
            ["--disable-auto-import"] = true,
            ["--disable-stdcall-fixup"] = true,
            ["--verbose"] = true,
            ["--disable-dynamicbase"] = true,
            ["--allow-multiple-definition"] = true,
            ["-o"] = true,
            ["--shared"] = true,
            ["--no-gc-sections"] = true,
            ["--high-entropy-va"] = true,
            ["--Bstatic"] = true,
            ["-m"] = true,
            ["--exclude-all-symbols"] = true,
            ["--disable-nxcompat"] = true,
            ["--help"] = true,
            ["--demangle"] = true,
            ["--disable-runtime-pseudo-reloc"] = true,
            ["-dy"] = true,
            ["-S"] = true,
            ["--enable-stdcall-fixup"] = true,
            ["--no-fatal-warnings"] = true,
            ["--whole-archive"] = true,
            ["--enable-auto-import"] = true,
            ["--strip-all"] = true,
            ["-dn"] = true,
            ["--disable-no-seh"] = true,
            ["--no-demangle"] = true,
            ["--kill-at"] = true,
            ["--no-dynamicbase"] = true,
            ["--fatal-warnings"] = true,
            ["-l"] = true,
            ["--enable-runtime-pseudo-reloc"] = true,
            ["--disable-tsaware"] = true,
            ["--gc-sections"] = true
        }
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolcxx"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["find_program_ndk_arch_armeabi-v7a_plat_android_checktoolsh"] = {
        ["clang++"] = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}