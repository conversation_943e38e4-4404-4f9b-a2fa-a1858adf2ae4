{
    ["rust_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true
    },
    ["ndk_arch_armeabi-v7a_plat_android"] = {
        cross = "arm-linux-androideabi-",
        plat = "android",
        ndk_sdkver = "21",
        ndkver = 25,
        arch = "armeabi-v7a",
        __checked = true,
        ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
        ndk_sysroot = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\sysroot]],
        bindir = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin]],
        llvm_toolchain = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64]],
        __global = true
    },
    ["envs_arch_armeabi-v7a_plat_android"] = {
        __global = true,
        plat = "android",
        arch = "armeabi-v7a",
        __checked = true
    },
    ["tool_target_leviutils_android_armeabi-v7a_cxx"] = {
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    },
    ["tool_target_leviutils_android_armeabi-v7a_sh"] = {
        toolchain_info = {
            arch = "armeabi-v7a",
            name = "ndk",
            plat = "android",
            cachekey = "ndk_arch_armeabi-v7a_plat_android"
        },
        toolname = "clangxx",
        program = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653\toolchains\llvm\prebuilt\windows-x86_64\bin\clang++]]
    }
}