{
    ["__toolchains_android_armeabi-v7a"] = {
        "envs",
        "ndk",
        "rust"
    },
    arch = "armeabi-v7a",
    builddir = "build",
    buildir = [[D:\LeviLaunchroidaaaaa\app\build\xmake]],
    ccache = true,
    clean = true,
    host = "windows",
    kind = "static",
    mode = "release",
    ndk = [[C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\25.2.9519653]],
    ndk_sdkver = "21",
    ndk_stdcxx = true,
    ndkver = 25,
    plat = "android"
}